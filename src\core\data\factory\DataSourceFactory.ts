import { DataSourceType } from '../types/DataSourceType.enum';

/**
 * Veri kaynağı factory pattern uygulaması
 * İstenen veri kaynağı tipine göre instance oluşturur
 */
export class DataSourceFactory {
    /**
     * Belirtilen veri kaynağı tipine göre instance oluşturur
     * @param type Veri kaynağı tipi
     * @returns IDataSource instance'ı
     */
    public static createDataSource(type: DataSourceType): IDataSource {
        console.log(`${type === DataSourceType.FIREBASE ? 'Firebase' : 'REST API'} veri kaynağı başlatılıyor...`);

        // İlk aşamada sadece konsol log
        return new MockDataSource(type);
    }
}

/**
 * Veri kaynağı interface'i
 * Tüm veri kaynakları bu interface'i implemente etmelidir
 */
export interface IDataSource {
    /** Veri kaynağı tipi */
    readonly type: DataSourceType;

    /**
     * Veri kaynağını başlatır
     */
    initialize(): void;

    /**
     * Veri kaynağının bağlı olup olmadığını kontrol eder
     * @returns boolean
     */
    isConnected(): boolean;
}

/**
 * Mock veri kaynağı implementasyonu
 * İlk aşamada test ve geliştirme için kullanılır
 */
class MockDataSource implements IDataSource {
    constructor(public readonly type: DataSourceType) { }

    /**
     * Veri kaynağını başlatır
     */
    initialize(): void {
        console.log(`${this.type} veri kaynağı başlatıldı`);
    }

    /**
     * Veri kaynağının bağlı olup olmadığını kontrol eder
     * @returns boolean - Her zaman true döner (mock implementasyon)
     */
    isConnected(): boolean {
        return true;
    }
}