import type { App } from 'vue';
import { CriticalErrorHandler } from '../service/CriticalErrorHandler.service';
import { ConnectionTester } from '../service/ConnectionTester.service';
import { DataSourceConfig } from '../config/DataSourceConfig.service';
import type { IConnectionResult, IConnectionTestOptions } from '../types/IConnectionResult.interface';

/**
 * Kritik hata yönetimi için Vue plugin'i
 * Uygulama başlangıcında bağlantı testi yapar ve hata durumunda yönetim sağlar
 */
export const CriticalErrorPlugin = {
    /**
     * Vue plugin install metodu
     * @param app Vue uygulaması instance'ı
     */
    install(app: App): void {
        console.log('[CriticalErrorPlugin] Kritik hata plugin\'i yükleniyor...');

        // Global error handler'ı ayarla
        setupGlobalErrorHandler(app);

        // Uygulama başlangıcında bağlantı testi yap
        initializeAppWithConnectionTest(app)
            .then((success: boolean) => {
                if (success) {
                    console.log('[CriticalErrorPlugin] Uygulama başarıyla başlatıldı');
                } else {
                    console.error('[CriticalErrorPlugin] Uygulama başlatılamadı');
                }
            })
            .catch((error: unknown) => {
                console.error('[CriticalErrorPlugin] Bağlantı testi sırasında hata:', error);
                CriticalErrorHandler.handleCriticalError(error as Error);
            });

        // Provide/inject ile erişim imkanı sun
        app.provide('criticalErrorHandler', CriticalErrorHandler);
        app.provide('connectionTester', ConnectionTester);

        console.log('[CriticalErrorPlugin] Kritik hata plugin\'i yüklendi');
    }
};

/**
 * Global error handler'ı ayarlar
 * @param app Vue uygulaması instance'ı
 */
function setupGlobalErrorHandler(app: App): void {
    // Global Vue error handler
    app.config.errorHandler = (err: unknown, vm: unknown, info: unknown) => {
        console.error('[Vue Error Handler]', err, vm, info);
        CriticalErrorHandler.handleCriticalError(err as Error);
    };

    // Global window error handler
    window.addEventListener('error', (event: ErrorEvent) => {
        console.error('[Window Error Handler]', event.error);
        CriticalErrorHandler.handleCriticalError(event.error);
    });

    // Global unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event: PromiseRejectionEvent) => {
        console.error('[Unhandled Promise Rejection]', event.reason);
        CriticalErrorHandler.handleCriticalError(event.reason as Error);
    });
}

/**
 * Uygulama başlangıcında bağlantı testi yapar
 * @param app Vue uygulaması instance'ı
 * @returns Promise<boolean> - Bağlantının başarılı olup olmadığı
 */
async function initializeAppWithConnectionTest(app: App): Promise<boolean> {
    try {
        // Önce ağ bağlantısını kontrol et
        const isNetworkAvailable = await ConnectionTester.checkNetworkConnection();
        if (!isNetworkAvailable) {
            throw new Error('Ağ bağlantısı mevcut değil');
        }

        // DataSourceConfig servisini al
        const dataSourceConfig = DataSourceConfig.getInstance();
        const config = dataSourceConfig.getConfig();

        // Bağlantı testi seçenekleri
        const testOptions: IConnectionTestOptions = {
            timeout: 15000, // 15 saniye
            verbose: true
        };

        // Bağlantı testi yap
        const connectionResult: IConnectionResult = await ConnectionTester.testConnection(
            config,
            testOptions
        );

        console.log('[CriticalErrorPlugin] Bağlantı testi sonucu:', connectionResult);

        if (!connectionResult.success) {
            throw new Error(connectionResult.error || 'Veri kaynağına bağlanılamadı');
        }

        return true;
    } catch (error) {
        // Hata durumunda CriticalErrorHandler'ı tetikle
        CriticalErrorHandler.handleCriticalError(error as Error);
        return false;
    }
}
};

/**
 * Composable function olarak kritik hata yöneticisine erişim
 * Component'lerde kolayca kullanılabilir
 */
import { inject } from 'vue';

/**
 * Kritik hata yöneticisine erişim sağlayan composable
 * @returns CriticalErrorHandler instance'ı
 */
export function useCriticalErrorHandler() {
    const errorHandler = inject('criticalErrorHandler') as typeof CriticalErrorHandler;

    if (!errorHandler) {
        throw new Error('Kritik hata yöneticisi bulunamadı. CriticalErrorPlugin\'in yüklenmiş olduğundan emin olun.');
    }

    return errorHandler;
}

/**
 * Bağlantı testcisine erişim sağlayan composable
 * @returns ConnectionTester instance'ı
 */
export function useConnectionTester() {
    const tester = inject('connectionTester') as typeof ConnectionTester;

    if (!tester) {
        throw new Error('Bağlantı testcisi bulunamadı. CriticalErrorPlugin\'in yüklenmiş olduğundan emin olun.');
    }

    return tester;
}