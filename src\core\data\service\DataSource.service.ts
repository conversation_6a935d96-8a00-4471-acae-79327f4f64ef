import { DataSourceConfig } from '../config/DataSourceConfig.service';
import { DataSourceFactory, type IDataSource } from '../factory/DataSourceFactory';
import { DataSourceType } from '../types/DataSourceType.enum';

/**
 * Global veri kaynağı service'i
 * Singleton pattern ile tek instance yönetimi
 * Tüm uygulama boyunca aynı veri kaynağı instance'ını kullanır
 */
export class DataSourceService {
    private static instance: DataSourceService;
    private dataSource: IDataSource;

    /**
     * Private constructor ile singleton pattern uygulanır
     */
    private constructor() {
        const config = DataSourceConfig.getInstance().getConfig();
        this.dataSource = DataSourceFactory.createDataSource(config.type);
        this.dataSource.initialize();
    }

    /**
     * Singleton instance'ı döndürür
     * @returns DataSourceService instance'ı
     */
    public static getInstance(): DataSourceService {
        if (!DataSourceService.instance) {
            DataSourceService.instance = new DataSourceService();
        }
        return DataSourceService.instance;
    }

    /**
     * Aktif veri kaynağı instance'ını döndürür
     * @returns IDataSource
     */
    public getDataSource(): IDataSource {
        return this.dataSource;
    }

    /**
     * Aktif veri kaynağı tipini döndürür
     * @returns DataSourceType
     */
    public getDataSourceType(): DataSourceType {
        return this.dataSource.type;
    }

    /**
     * Veri kaynağının bağlı olup olmadığını kontrol eder
     * @returns boolean
     */
    public isConnected(): boolean {
        return this.dataSource.isConnected();
    }

    /**
     * Veri kaynağının yeniden başlatılmasını sağlar
     * @param type Yeni veri kaynağı tipi (opsiyonel)
     */
    public restart(type?: DataSourceType): void {
        if (type) {
            this.dataSource = DataSourceFactory.createDataSource(type);
        }
        this.dataSource.initialize();
        console.log(`${this.dataSource.type} veri kaynağı yeniden başlatıldı`);
    }
}