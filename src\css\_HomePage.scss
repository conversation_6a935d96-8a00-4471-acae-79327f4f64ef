/**
 * @file _HomePage.scss
 * @description HomePage bileşeni için mobil öncelikli SCSS stilleri
 * 
 * <AUTHOR> Türk Yazılım Mimarı
 * @version 1.0.0
 */

// Mobil öncelikli tasarım için breakpoint tanımlamaları
$breakpoints: (
  mobile: 768px,
  tablet: 1024px,
  desktop: 1200px,
);

// Renk paleti
$colors: (
  primary: #3498db,
  primary-dark: #2980b9,
  success: #27ae60,
  success-dark: #229954,
  background: #f8f9fa,
  card-bg: #ffffff,
  text-primary: #2c3e50,
  text-secondary: #7f8c8d,
  border: #e9ecef,
  code-bg: #bdc3c7,
);

// Font boyutları
$font-sizes: (
  xs: 0.75rem,
  sm: 0.875rem,
  base: 1rem,
  lg: 1.125rem,
  xl: 1.25rem,
  '2xl': 1.5rem,
  '3xl': 1.875rem,
  '4xl': 2.25rem,
);

// Spacing
$spacing: (
  xs: 0.25rem,
  sm: 0.5rem,
  md: 1rem,
  lg: 1.5rem,
  xl: 2rem,
  '2xl': 3rem,
);

// Mixin: Responsive tasarım
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}

// Mixin: Buton stilleri
@mixin button-styles($bg-color, $hover-color) {
  background-color: $bg-color;
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-size: map-get($font-sizes, base);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  &:hover {
    background-color: $hover-color;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
}

// Mixin: Kart stilleri
@mixin card-styles {
  background-color: map-get($colors, card-bg);
  border-radius: 0.75rem;
  padding: map-get($spacing, lg);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid map-get($colors, border);
}

// Ana sayfa konteyneri
.home-page {
  min-height: 100vh;
  background-color: map-get($colors, background);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: map-get($colors, text-primary);
  line-height: 1.6;
}

// Sayfa başlığı
.page-header {
  text-align: center;
  padding: map-get($spacing, '2xl') map-get($spacing, md);
  background: linear-gradient(
    135deg,
    map-get($colors, primary) 0%,
    map-get($colors, primary-dark) 100%
  );
  color: white;
  margin-bottom: map-get($spacing, xl);

  h1 {
    font-size: map-get($font-sizes, '3xl');
    font-weight: 700;
    margin: 0 0 map-get($spacing, sm) 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .page-description {
    font-size: map-get($font-sizes, lg);
    opacity: 0.9;
    margin: 0;
  }
}

// İçerik konteyneri
.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 map-get($spacing, md);

  @include respond-to(tablet) {
    padding: 0 map-get($spacing, lg);
  }

  @include respond-to(desktop) {
    padding: 0 map-get($spacing, '2xl');
  }
}

// Bilgi kartı
.info-card {
  @include card-styles;
  margin-bottom: map-get($spacing, xl);
  text-align: center;

  h3 {
    font-size: map-get($font-sizes, xl);
    font-weight: 600;
    margin: 0 0 map-get($spacing, md) 0;
    color: map-get($colors, text-primary);
  }

  .data-source-type {
    font-size: map-get($font-sizes, '2xl');
    font-weight: 700;
    color: map-get($colors, primary);
    margin: map-get($spacing, md) 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .connection-status {
    font-size: map-get($font-sizes, lg);
    font-weight: 500;
    color: map-get($colors, success);
    margin: 0;
  }
}

// Kontrol butonları
.controls {
  margin-bottom: map-get($spacing, xl);

  h3 {
    font-size: map-get($font-sizes, xl);
    font-weight: 600;
    margin: 0 0 map-get($spacing, md) 0;
    color: map-get($colors, text-primary);
  }

  .button-group {
    display: flex;
    flex-direction: column;
    gap: map-get($spacing, sm);

    @include respond-to(tablet) {
      flex-direction: row;
      justify-content: center;
    }
  }
}

// Veri kaynağı butonları
.data-source-btn {
  @include button-styles(map-get($colors, primary), map-get($colors, primary-dark));
  width: 100%;

  @include respond-to(tablet) {
    width: auto;
    min-width: 180px;
  }

  &.active {
    background-color: map-get($colors, success);
    &:hover {
      background-color: map-get($colors, success-dark);
    }
  }
}

// Kullanım bilgileri
.usage-info {
  @include card-styles;

  h3 {
    font-size: map-get($font-sizes, xl);
    font-weight: 600;
    margin: 0 0 map-get($spacing, md) 0;
    color: map-get($colors, text-primary);
  }

  ul {
    margin: 0;
    padding-left: map-get($spacing, lg);
    list-style: none;

    li {
      margin-bottom: map-get($spacing, sm);
      font-size: map-get($font-sizes, base);
      line-height: 1.5;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  code {
    background-color: map-get($colors, code-bg);
    padding: map-get($spacing, xs) map-get($spacing, sm);
    border-radius: 0.25rem;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
    font-size: map-get($font-sizes, sm);
    color: map-get($colors, text-primary);
    border: 1px solid map-get($colors, border);
  }
}

// Mobil için optimize edilmiş stiller
@media (max-width: 767px) {
  .home-page {
    .page-header {
      padding: map-get($spacing, xl) map-get($spacing, md);

      h1 {
        font-size: map-get($font-sizes, '2xl');
      }

      .page-description {
        font-size: map-get($font-sizes, base);
      }
    }

    .content-container {
      padding: 0 map-get($spacing, sm);
    }

    .info-card,
    .usage-info {
      padding: map-get($spacing, md);
    }

    .controls h3 {
      font-size: map-get($font-sizes, lg);
    }

    .data-source-btn {
      padding: map-get($spacing, md) map-get($spacing, lg);
      font-size: map-get($font-sizes, base);
    }
  }
}

// Yüksek kontrast mod desteği
@media (prefers-contrast: high) {
  .home-page {
    .info-card,
    .usage-info {
      border-width: 2px;
    }

    .data-source-btn {
      border: 2px solid currentColor;
    }
  }
}

// Koyu mod desteği
@media (prefers-color-scheme: dark) {
  .home-page {
    background-color: #1a1a1a;
    color: #e0e0e0;

    .info-card,
    .usage-info {
      background-color: #2d2d2d;
      border-color: #404040;
    }

    .data-source-type {
      color: #64b5f6;
    }

    .connection-status {
      color: #81c784;
    }

    code {
      background-color: #404040;
      border-color: #555555;
    }
  }
}
