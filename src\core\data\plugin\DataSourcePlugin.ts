import type { App } from 'vue';
import { DataSourceService } from '../service/DataSource.service';

/**
 * Vue plugin olarak veri kaynağı entegrasyonu
 * Uygulama başlangıcında otomatik olarak veri kaynağını başlatır
 * Hem global properties hem de provide/inject ile erişim imkanı sunar
 */
export const DataSourcePlugin = {
    /**
     * Vue plugin install metodu
     * @param app Vue uygulaması instance'ı
     */
    install(app: App): void {
        // Veri kaynağı service'ini başlat
        const dataSourceService = DataSourceService.getInstance();

        // Global properties'e ekle
        app.config.globalProperties.$dataSource = dataSourceService;

        // provide ile component'lerde erişim imkanı sun
        app.provide('dataSource', dataSourceService);

        console.log('Veri kaynağı plugin\'i yüklendi');
    }
};

/**
 * Composable function olarak veri kaynağı service'ine erişim
 * Component'lerde kolayca kullanılabilir
 */
import { inject } from 'vue';

/**
 * Veri kaynağı service'ine erişim sağlayan composable
 * @returns DataSourceService instance'ı
 */
export function useDataSource() {
    const dataSource = inject<DataSourceService>('dataSource');

    if (!dataSource) {
        throw new Error('Veri kaynağı service\'i bulunamadı. DataSourcePlugin\'in yüklenmiş olduğundan emin olun.');
    }

    return dataSource;
}