import { DataSourceType } from './DataSourceType.enum';

/**
 * Genel veri kaynağı yapılandırması.
 */
export interface IDataSourceConfig {
    type: DataSourceType;
    enabled: boolean;
}

/**
 * Firebase yapılandırma ayarları.
 */
export interface IFirebaseConfig {
    apiKey: string;
    authDomain: string;
    databaseURL: string;
    projectId: string;
    storageBucket: string;
    messagingSenderId: string;
    appId: string;
}

/**
 * REST API yapılandırma ayarları.
 */
export interface IRestApiConfig {
    baseUrl: string;
    timeout: number;
}