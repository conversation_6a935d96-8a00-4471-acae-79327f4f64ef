/**
 * import.meta.env için özel ortam değişkenleri tip tanımları.
 * Standart Vite ortam değişkenleri env.d.ts içinde tanımlanmıştır.
 */
export interface CustomImportMetaEnv extends ImportMetaEnv {
    /** Veri kaynağ<PERSON> tipi (firebase veya restapi) */
    readonly VITE_DATA_SOURCE: string;

    // Firebase Config
    readonly VITE_FIREBASE_API_KEY: string;
    readonly VITE_FIREBASE_AUTH_DOMAIN: string;
    readonly VITE_FIREBASE_DATABASE_URL: string;
    readonly VITE_FIREBASE_PROJECT_ID: string;
    readonly VITE_FIREBASE_STORAGE_BUCKET: string;
    readonly VITE_FIREBASE_MESSAGING_SENDER_ID: string;
    readonly VITE_FIREBASE_APP_ID: string;

    // REST API Config
    readonly VITE_REST_API_BASE_URL: string;
    readonly VITE_REST_API_TIMEOUT: string;
}