import type { IDataSourceConfig, IFirebaseConfig, IRestApiConfig } from '../types/IDataSourceConfig.interface';
import { DataSourceType } from '../types/DataSourceType.enum';
import type { CustomImportMetaEnv } from '../types/ImportMetaEnv.interface';

/**
 * .env dosyasından veri kaynağı ayarlarını okuyan servis.
 * Singleton pattern ile tek instance yönetimi sağlar.
 */
export class DataSourceConfig {
    private static instance: DataSourceConfig;
    private config: IDataSourceConfig;
    private firebaseConfig: IFirebaseConfig;
    private restApiConfig: IRestApiConfig;

    private constructor() {
        const env = import.meta.env as unknown as CustomImportMetaEnv;
        this.config = this.loadConfig(env);
        this.firebaseConfig = this.loadFirebaseConfig(env);
        this.restApiConfig = this.loadRestApiConfig(env);
    }

    public static getInstance(): DataSourceConfig {
        if (!DataSourceConfig.instance) {
            DataSourceConfig.instance = new DataSourceConfig();
        }
        return DataSourceConfig.instance;
    }

    private loadConfig(env: CustomImportMetaEnv): IDataSourceConfig {
        const dataSource = env.VITE_DATA_SOURCE?.toLowerCase() || 'firebase';
        return {
            type: dataSource === 'firebase' ? DataSourceType.FIREBASE : DataSourceType.REST_API,
            enabled: true,
        };
    }

    private loadFirebaseConfig(env: CustomImportMetaEnv): IFirebaseConfig {
        return {
            apiKey: env.VITE_FIREBASE_API_KEY,
            authDomain: env.VITE_FIREBASE_AUTH_DOMAIN,
            databaseURL: env.VITE_FIREBASE_DATABASE_URL,
            projectId: env.VITE_FIREBASE_PROJECT_ID,
            storageBucket: env.VITE_FIREBASE_STORAGE_BUCKET,
            messagingSenderId: env.VITE_FIREBASE_MESSAGING_SENDER_ID,
            appId: env.VITE_FIREBASE_APP_ID,
        };
    }

    private loadRestApiConfig(env: CustomImportMetaEnv): IRestApiConfig {
        return {
            baseUrl: env.VITE_REST_API_BASE_URL,
            timeout: Number(env.VITE_REST_API_TIMEOUT),
        };
    }

    public getConfig(): IDataSourceConfig {
        return this.config;
    }

    public getFirebaseConfig(): IFirebaseConfig {
        return this.firebaseConfig;
    }

    public getRestApiConfig(): IRestApiConfig {
        return this.restApiConfig;
    }

    public getDataSourceType(): DataSourceType {
        return this.config.type;
    }

    public isEnabled(): boolean {
        return this.config.enabled;
    }
}