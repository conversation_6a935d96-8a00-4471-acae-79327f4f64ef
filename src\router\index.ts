import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('../pages/HomePage.vue'),
      meta: {
        title: 'Ana Sayfa',
        description: 'Veri Kaynağı Yönetim Sistemi'
      }
    }
  ],
})

// <PERSON>fa başlığını dinamik olarak güncelleme
router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = `${to.meta.title} - Vue DataSource`
  }
  next()
})

export default router
