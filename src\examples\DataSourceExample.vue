<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useDataSource } from '../core/data/plugin/DataSourcePlugin'
import { DataSourceType } from '../core/data/types/DataSourceType.enum'

const dataSource = useDataSource()
const dataSourceType = ref<DataSourceType>(dataSource.getDataSourceType())
const isConnected = ref<boolean>(false)

onMounted(() => {
  // Veri kaynağının bağlantı durumunu kontrol et
  isConnected.value = dataSource.isConnected()

  // Konsola log yazdır
  console.log(
    'Aktif veri kaynağı:',
    dataSourceType.value === DataSourceType.FIREBASE ? 'Firebase' : 'REST API',
  )
  console.log('Bağlantı durumu:', isConnected.value)
})

const restartDataSource = (type: DataSourceType) => {
  dataSource.restart(type)
  dataSourceType.value = dataSource.getDataSourceType()
  isConnected.value = dataSource.isConnected()

  console.log(
    'Veri kaynağı yeniden başlatıldı:',
    dataSourceType.value === DataSourceType.FIREBASE ? 'Firebase' : 'REST API',
  )
}
</script>

<template>
  <div class="data-source-example">
    <h2>Veri Kaynağı Örneği</h2>

    <div class="info-card">
      <h3>Aktif Veri Kaynağı</h3>
      <p class="data-source-type">
        {{ dataSourceType === DataSourceType.FIREBASE ? 'Firebase' : 'REST API' }}
      </p>
      <p class="connection-status">Bağlantı: {{ isConnected ? 'Bağlı' : 'Bağlı Değil' }}</p>
    </div>

    <div class="controls">
      <h3>Veri Kaynağını Değiştir</h3>
      <button
        @click="restartDataSource(DataSourceType.FIREBASE)"
        :class="{ active: dataSourceType === DataSourceType.FIREBASE }"
      >
        Firebase Kullan
      </button>
      <button
        @click="restartDataSource(DataSourceType.REST_API)"
        :class="{ active: dataSourceType === DataSourceType.REST_API }"
      >
        REST API Kullan
      </button>
    </div>

    <div class="usage-info">
      <h3>Kullanım Bilgileri</h3>
      <ul>
        <li>Global erişim: <code>app.config.globalProperties.$dataSource</code></li>
        <li>Composable kullanımı: <code>useDataSource()</code></li>
        <li>Provide/Inject: <code>inject('dataSource')</code></li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
.data-source-example {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.info-card {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.data-source-type {
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin: 10px 0;
}

.connection-status {
  font-size: 16px;
  color: #27ae60;
}

.controls {
  margin-bottom: 20px;
}

.controls button {
  padding: 10px 20px;
  margin-right: 10px;
  border: none;
  border-radius: 4px;
  background: #3498db;
  color: white;
  cursor: pointer;
  transition: background 0.3s;
}

.controls button:hover {
  background: #2980b9;
}

.controls button.active {
  background: #27ae60;
}

.usage-info {
  background: #ecf0f1;
  padding: 15px;
  border-radius: 8px;
}

.usage-info h3 {
  margin-top: 0;
  color: #2c3e50;
}

.usage-info ul {
  margin: 10px 0;
  padding-left: 20px;
}

.usage-info code {
  background: #bdc3c7;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}
</style>
