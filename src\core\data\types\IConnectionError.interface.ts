/**
 * <PERSON><PERSON>lantı hata tipleri
 */
export enum ConnectionErrorType {
    NETWORK_ERROR = 'NETWORK_ERROR',
    TIMEOUT_ERROR = 'TIMEOUT_ERROR',
    AUTH_ERROR = 'AUTH_ERROR',
    CONFIG_ERROR = 'CONFIG_ERROR',
    SERVER_ERROR = 'SERVER_ERROR',
    UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * Bağlantı hatası detayları
 */
export interface IConnectionError {
    /** Hata tipi */
    type: ConnectionErrorType;
    /** Hata mesajı */
    message: string;
    /** Hata detayları */
    details?: Record<string, unknown>;
    /** Hata zamanı */
    timestamp: Date;
    /** Hata kaynağı */
    source: 'firebase' | 'restapi' | 'config' | 'unknown';
}

/**
 * Kritik hata tipleri
 */
export enum CriticalErrorType {
    DATA_SOURCE_UNAVAILABLE = 'DATA_SOURCE_UNAVAILABLE',
    CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
    INITIALIZATION_FAILED = 'INITIALIZATION_FAILED',
    NETWORK_UNAVAILABLE = 'NETWORK_UNAVAILABLE'
}

/**
 * Kritik hata detayları
 */
export interface ICriticalError {
    /** Hata tipi */
    type: CriticalErrorType;
    /** Hata mesajı */
    message: string;
    /** Hata detayları */
    details?: Record<string, unknown>;
    /** Hata zamanı */
    timestamp: Date;
    /** Çözüm önerileri */
    suggestions?: string[];
    /** Yeniden denenebilir mi */
    retryable: boolean;
}