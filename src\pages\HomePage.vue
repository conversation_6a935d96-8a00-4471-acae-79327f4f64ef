<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useDataSource } from '../core/data/plugin/DataSourcePlugin'
import { DataSourceType } from '../core/data/types/DataSourceType.enum'
import type { IHomePageProps } from '../components/IHomePage.interface'

/**
 * @component HomePage
 * @description Ana sayfa bileşeni, veri kaynağı yönetimini gösterir
 *
 * <AUTHOR> Türk Yazılım Mimarı
 * @version 1.0.0
 */

// Props tanımlama
const props = withDefaults(defineProps<IHomePageProps>(), {
  title: 'Ana Sayfa',
  description: 'Veri Kaynağı Yönetim Sistemi',
})

// Events tanımlama
const emit = defineEmits<{
  dataSourceChange: [type: DataSourceType]
  connectionStatusChange: [isConnected: boolean]
}>()

// Reactive state'ler
const dataSource = useDataSource()
const dataSourceType = ref<DataSourceType>(dataSource.getDataSourceType())
const isConnected = ref<boolean>(false)

/**
 * @description Bileşen yüklendiğinde veri kaynağı bağlantısını kontrol eder
 */
onMounted(() => {
  // Veri kaynağının bağlantı durumunu kontrol et
  isConnected.value = dataSource.isConnected()

  // Konsola log yazdır
  console.log(
    'Aktif veri kaynağı:',
    dataSourceType.value === DataSourceType.FIREBASE ? 'Firebase' : 'REST API',
  )
  console.log('Bağlantı durumu:', isConnected.value)
})

/**
 * @description Veri kaynağını yeniden başlatır
 * @param type - Yeni veri kaynağı tipi
 */
const restartDataSource = (type: DataSourceType) => {
  dataSource.restart(type)
  dataSourceType.value = dataSource.getDataSourceType()
  isConnected.value = dataSource.isConnected()

  // Event emit et
  emit('dataSourceChange', type)
  emit('connectionStatusChange', isConnected.value)

  console.log(
    'Veri kaynağı yeniden başlatıldı:',
    dataSourceType.value === DataSourceType.FIREBASE ? 'Firebase' : 'REST API',
  )
}
</script>

<template>
  <div class="home-page">
    <header class="page-header">
      <h1>{{ props.title }}</h1>
      <p class="page-description">{{ props.description }}</p>
    </header>

    <div class="content-container">
      <div class="info-card">
        <h3>Aktif Veri Kaynağı</h3>
        <p class="data-source-type">
          {{ dataSourceType === DataSourceType.FIREBASE ? 'Firebase' : 'REST API' }}
        </p>
        <p class="connection-status">Bağlantı: {{ isConnected ? 'Bağlı' : 'Bağlı Değil' }}</p>
      </div>

      <div class="controls">
        <h3>Veri Kaynağını Değiştir</h3>
        <div class="button-group">
          <button
            @click="restartDataSource(DataSourceType.FIREBASE)"
            :class="{ active: dataSourceType === DataSourceType.FIREBASE }"
            class="data-source-btn"
          >
            Firebase Kullan
          </button>
          <button
            @click="restartDataSource(DataSourceType.REST_API)"
            :class="{ active: dataSourceType === DataSourceType.REST_API }"
            class="data-source-btn"
          >
            REST API Kullan
          </button>
        </div>
      </div>

      <div class="usage-info">
        <h3>Kullanım Bilgileri</h3>
        <ul>
          <li>Global erişim: <code>app.config.globalProperties.$dataSource</code></li>
          <li>Composable kullanımı: <code>useDataSource()</code></li>
          <li>Provide/Inject: <code>inject('dataSource')</code></li>
        </ul>
      </div>
    </div>
  </div>
</template>
