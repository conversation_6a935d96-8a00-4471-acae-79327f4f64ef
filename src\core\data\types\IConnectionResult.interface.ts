/**
 * Bağlantı testi sonucu için tip tanımlamaları
 */
export interface IConnectionResult {
    /** Bağlantının başarılı olup olmadığı */
    success: boolean;
    /** Bağlantı süresi (milisaniye cinsinden) */
    latency?: number;
    /** Hata mesajı (varsa) */
    error?: string;
    /** Hata detayları (varsa) */
    errorDetails?: Record<string, unknown>;
    /** Test zamanı */
    timestamp: Date;
}

/**
 * Bağlantı testi seçenekleri
 */
export interface IConnectionTestOptions {
    /** Zaman aşımı (milisaniye cinsinden) */
    timeout?: number;
    /** Görünür test modu (debug için) */
    verbose?: boolean;
}