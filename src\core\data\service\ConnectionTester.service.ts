import type { IDataSourceConfig, IFirebaseConfig, IRestApiConfig } from '../types/IDataSourceConfig.interface';
import type { IConnectionResult, IConnectionTestOptions } from '../types/IConnectionResult.interface';
import type { IConnectionError } from '../types/IConnectionError.interface';
import { DataSourceType } from '../types/DataSourceType.enum';
import { ConnectionErrorType } from '../types/IConnectionError.interface';
import { DataSourceConfig } from '../config/DataSourceConfig.service';

/**
 * Veri kaynağı bağlantı test servisi
 * Firebase ve REST API için gerçek bağlantı testleri yapar
 */
export class ConnectionTester {
    private static readonly DEFAULT_TIMEOUT = 10000; // 10 saniye
    private static readonly DEFAULT_TEST_ENDPOINT = '/health';

    /**
     * Firebase bağlantısını test eder
     * @param config Firebase yapılandırması
     * @param testOptions Test seçenekleri
     * @returns Promise<IConnectionResult>
     */
    public static async testFirebaseConnection(
        config: IFirebaseConfig,
        testOptions: IConnectionTestOptions = {}
    ): Promise<IConnectionResult> {
        const startTime = Date.now();
        const timeout = testOptions.timeout || this.DEFAULT_TIMEOUT;

        try {
            // Firebase SDK ile bağlantı testi
            // Gerçek implementasyonda Firebase SDK kullanılacak
            const mockResult = await this.mockFirebaseTest(config, timeout);

            const latency = Date.now() - startTime;

            return {
                success: mockResult.success,
                latency,
                error: mockResult.error,
                errorDetails: mockResult.errorDetails,
                timestamp: new Date()
            };
        } catch (error) {
            const latency = Date.now() - startTime;
            const connectionError = this.formatError(error, 'firebase');

            return {
                success: false,
                latency,
                error: connectionError.message,
                errorDetails: connectionError.details,
                timestamp: new Date()
            };
        }
    }

    /**
     * REST API bağlantısını test eder
     * @param config REST API yapılandırması
     * @param testOptions Test seçenekleri
     * @returns Promise<IConnectionResult>
     */
    public static async testRestApiConnection(
        config: IRestApiConfig,
        testOptions: IConnectionTestOptions = {}
    ): Promise<IConnectionResult> {
        const startTime = Date.now();
        const timeout = testOptions.timeout || this.DEFAULT_TIMEOUT;
        const endpoint = `${config.baseUrl}${this.DEFAULT_TEST_ENDPOINT}`;

        try {
            // Fetch API ile bağlantı testi
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeout);

            const response = await fetch(endpoint, {
                method: 'GET',
                signal: controller.signal,
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const latency = Date.now() - startTime;

            return {
                success: true,
                latency,
                timestamp: new Date()
            };
        } catch (error) {
            const latency = Date.now() - startTime;
            const connectionError = this.formatError(error, 'restapi');

            return {
                success: false,
                latency,
                error: connectionError.message,
                errorDetails: connectionError.details,
                timestamp: new Date()
            };
        }
    }

    /**
     * Genel bağlantı testi yapar
     * @param config Veri kaynağı yapılandırması
     * @param testOptions Test seçenekleri
     * @returns Promise<IConnectionResult>
     */
    public static async testConnection(
        config: IDataSourceConfig,
        testOptions: IConnectionTestOptions = {}
    ): Promise<IConnectionResult> {
        if (testOptions.verbose) {
            console.log(`[${new Date().toISOString()}] Bağlantı testi başlatılıyor: ${config.type}`);
        }

        switch (config.type) {
            case DataSourceType.FIREBASE:
                return this.testFirebaseConnection(config, testOptions);
            case DataSourceType.REST_API:
                return this.testRestApiConnection(config, testOptions);
            default:
                const unknownError: IConnectionError = {
                    type: ConnectionErrorType.CONFIG_ERROR,
                    message: `Bilinmeyen veri kaynağı tipi: ${config.type}`,
                    timestamp: new Date(),
                    source: 'config'
                };

                return {
                    success: false,
                    error: unknownError.message,
                    errorDetails: { error: unknownError },
                    timestamp: new Date()
                };
        }
    }

    /**
     * Ağ bağlantısını kontrol eder
     * @returns Promise<boolean>
     */
    public static async checkNetworkConnection(): Promise<boolean> {
        try {
            // Basit internet bağlantısı kontrolü
            if (!navigator.onLine) {
                return false;
            }

            // DNS çözümleme testi
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            await fetch('https://www.google.com/favicon.ico', {
                method: 'HEAD',
                mode: 'no-cors',
                signal: controller.signal
            });

            clearTimeout(timeoutId);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * Hata nesnesini formatlar
     * @param error Orijinal hata
     * @param source Hata kaynağı
     * @returns IConnectionError
     */
    private static formatError(error: unknown, source: 'firebase' | 'restapi' | 'config' | 'unknown'): IConnectionError {
        let type: ConnectionErrorType = ConnectionErrorType.UNKNOWN_ERROR;
        let message = 'Bilinmeyen hata';
        let details: Record<string, unknown> = {};

        if (error instanceof Error) {
            message = error.message;

            if (error.name === 'AbortError' || error.message.includes('timeout')) {
                type = ConnectionErrorType.TIMEOUT_ERROR;
            } else if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
                type = ConnectionErrorType.NETWORK_ERROR;
            } else if (error.message.includes('401') || error.message.includes('403')) {
                type = ConnectionErrorType.AUTH_ERROR;
            } else if (error.message.includes('500') || error.message.includes('502') || error.message.includes('503')) {
                type = ConnectionErrorType.SERVER_ERROR;
            }
        }

        if (error instanceof Response) {
            details = {
                status: error.status,
                statusText: error.statusText,
                url: error.url
            };
        }

        return {
            type,
            message,
            details,
            timestamp: new Date(),
            source
        };
    }

    /**
     * Firebase testi için mock implementasyon
     * Gerçek implementasyonda Firebase SDK kullanılacak
     */
    private static async mockFirebaseTest(
        config: IFirebaseConfig,
        timeout: number
    ): Promise<{ success: boolean; error?: string; errorDetails?: Record<string, unknown> }> {
        return new Promise((resolve) => {
            setTimeout(() => {
                // Gerçek Firebase bağlantı kontrolü burada yapılacak
                // Şimdilik başarılı sonuç dönüyoruz
                resolve({
                    success: true
                });
            }, 1000); // 1 saniye bekleme
        });
    }
}