/**
 * @file app.scss
 * @description Uygulama genelindeki SCSS stilleri ve component importları
 *
 * <AUTHOR> Türk Yazılım Mimarı
 * @version 1.0.0
 */

// SCSS değişkenleri
$primary-color: #3498db;
$secondary-color: #2c3e50;
$success-color: #27ae60;
$danger-color: #e74c3c;
$warning-color: #f39c12;
$info-color: #17a2b8;

$font-size-xs: 0.75rem;
$font-size-sm: 0.875rem;
$font-size-base: 1rem;
$font-size-lg: 1.125rem;
$font-size-xl: 1.25rem;
$font-size-2xl: 1.5rem;
$font-size-3xl: 1.875rem;
$font-size-4xl: 2.25rem;

$spacing-xs: 0.25rem;
$spacing-sm: 0.5rem;
$spacing-md: 1rem;
$spacing-lg: 1.5rem;
$spacing-xl: 2rem;
$spacing-2xl: 3rem;

// Component stillerini import etme
@import './_HomePage.scss';

// Global değişkenler ve mixin'ler
:root {
  // CSS değişkenleri (SCSS değişkenlerinden senkronize)
  --primary-color: #{$primary-color};
  --secondary-color: #{$secondary-color};
  --success-color: #{$success-color};
  --danger-color: #{$danger-color};
  --warning-color: #{$warning-color};
  --info-color: #{$info-color};

  // Font boyutları
  --font-size-xs: #{$font-size-xs};
  --font-size-sm: #{$font-size-sm};
  --font-size-base: #{$font-size-base};
  --font-size-lg: #{$font-size-lg};
  --font-size-xl: #{$font-size-xl};
  --font-size-2xl: #{$font-size-2xl};
  --font-size-3xl: #{$font-size-3xl};
  --font-size-4xl: #{$font-size-4xl};

  // Spacing
  --spacing-xs: #{$spacing-xs};
  --spacing-sm: #{$spacing-sm};
  --spacing-md: #{$spacing-md};
  --spacing-lg: #{$spacing-lg};
  --spacing-xl: #{$spacing-xl};
  --spacing-2xl: #{$spacing-2xl};
}

// Global stiller
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.6;
}

body {
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8f9fa;
  color: $secondary-color;
}

// Typography
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: $spacing-md;
}

h1 {
  font-size: $font-size-3xl;
}
h2 {
  font-size: $font-size-2xl;
}
h3 {
  font-size: $font-size-xl;
}
h4 {
  font-size: $font-size-lg;
}
h5 {
  font-size: $font-size-base;
}
h6 {
  font-size: $font-size-sm;
}

p {
  margin-bottom: $spacing-md;
  line-height: 1.6;
}

// Linkler
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s ease;

  &:hover {
    color: darken($primary-color, 10%);
  }
}

// Buton stilleri
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-sm $spacing-md;
  border: none;
  border-radius: 0.375rem;
  font-size: $font-size-base;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  white-space: nowrap;

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.3);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.btn-primary {
  background-color: $primary-color;
  color: white;

  &:hover:not(:disabled) {
    background-color: darken($primary-color, 10%);
  }
}

.btn-success {
  background-color: $success-color;
  color: white;

  &:hover:not(:disabled) {
    background-color: darken($success-color, 10%);
  }
}

// Kart stilleri
.card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: $spacing-lg;
  margin-bottom: $spacing-lg;
}

// Form elemanları
input,
textarea,
select {
  width: 100%;
  padding: $spacing-sm $spacing-md;
  border: 1px solid #ddd;
  border-radius: 0.375rem;
  font-size: $font-size-base;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
  }
}

// Grid ve Flexbox yardımcıları
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 $spacing-md;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(-1 * $spacing-md);
}

.col {
  flex: 1;
  padding: 0 $spacing-md;
  margin-bottom: $spacing-md;
}

// Animasyonlar
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

// Vue Router sayfa geçişi animasyonları
// App.vue'de kullanılan transition component'i için optimize edilmiş CSS
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// Animasyonların daha akıcı çalışması için ek ayarlar
.app-router-view {
  position: relative;
  min-height: 100vh;
}

// Animasyon sırasında oluşabilecek layout shift'leri önlemek için
.fade-enter-active,
.fade-leave-active {
  will-change: opacity;
}

// Responsive yardımcıları
@media (max-width: 768px) {
  .container {
    padding: 0 $spacing-sm;
  }

  .row {
    margin: 0;
  }

  .col {
    padding: 0;
    margin-bottom: $spacing-md;
  }
}

// Yüksek kontrast mod desteği
@media (prefers-contrast: high) {
  :root {
    --primary-color: #0066cc;
    --secondary-color: #000000;
  }

  .btn {
    border: 2px solid currentColor;
  }
}

// Koyu mod desteği
@media (prefers-color-scheme: dark) {
  :root {
    --primary-color: #64b5f6;
    --secondary-color: #e0e0e0;
  }

  body {
    background-color: #1a1a1a;
    color: $secondary-color;
  }

  .card {
    background-color: #2d2d2d;
    border-color: #404040;
  }

  input,
  textarea,
  select {
    background-color: #2d2d2d;
    border-color: #404040;
    color: $secondary-color;
  }
}
