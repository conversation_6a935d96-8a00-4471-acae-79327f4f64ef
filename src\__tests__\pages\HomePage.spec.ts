import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import type { Router } from 'vue-router'
import type { Pinia } from 'pinia'
import HomePage from '../../pages/HomePage.vue'
import { createPinia } from 'pinia'

// Mock DataSourcePlugin
const mockDataSource = {
    getDataSourceType: vi.fn(() => 'FIREBASE' as const),
    isConnected: vi.fn(() => true),
    restart: vi.fn()
}

vi.mock('../../core/data/plugin/DataSourcePlugin', () => ({
    useDataSource: () => mockDataSource
}))

describe('HomePage.vue', () => {
    let wrapper: ReturnType<typeof mount>
    let router: Router
    let pinia: Pinia

    beforeEach(() => {
        // Test ortamı için router ve pinia oluştur
        router = createRouter({
            history: createWebHistory(),
            routes: [
                {
                    path: '/',
                    name: 'Home',
                    component: HomePage
                }
            ]
        })

        pinia = createPinia()

        // Wrapper'ı oluştur
        wrapper = mount(HomePage, {
            global: {
                plugins: [router, pinia],
                stubs: {
                    'router-link': true
                }
            }
        })
    })

    it('su anki bileşen dogru sekilde render edilmeli', () => {
        expect(wrapper.exists()).toBe(true)
    })

    it('baslik ve açiklama varsayilan degerlerle gösterilmeli', () => {
        expect(wrapper.find('h1').text()).toBe('Ana Sayfa')
        expect(wrapper.find('.page-description').text()).toBe('Veri Kaynağı Yönetim Sistemi')
    })

    it('veri kaynagi tipi dogru sekilde gösterilmeli', () => {
        expect(wrapper.find('.data-source-type').text()).toBe('Firebase')
    })

    it('baglanti durumu dogru sekilde gösterilmeli', () => {
        expect(wrapper.find('.connection-status').text()).toBe('Bağlantı: Bağlı')
    })

    it('Firebase butonu tiklandiginda dogru fonksiyon cagrilmali', async () => {
        const firebaseButton = wrapper.find('.data-source-btn')
        await firebaseButton.trigger('click')

        // useDataSource mock'ındaki restart fonksiyonu çağrıldı mı?
        expect(mockDataSource.restart).toHaveBeenCalledWith('FIREBASE')
    })

    it('REST API butonu tiklandiginda dogru fonksiyon cagrilmali', async () => {
        const restApiButton = wrapper.findAll('.data-source-btn')[1]
        await restApiButton.trigger('click')

        // useDataSource mock'ındaki restart fonksiyonu çağrıldı mı?
        expect(mockDataSource.restart).toHaveBeenCalledWith('REST_API')
    })

    it('aktif buton dogru sekilde stilendirilmeli', () => {
        const firebaseButton = wrapper.find('.data-source-btn')
        expect(firebaseButton.classes()).toContain('active')
    })

    it('kullanim bilgileri dogru sekilde gösterilmeli', () => {
        const usageInfo = wrapper.find('.usage-info')
        expect(usageInfo.exists()).toBe(true)

        const listItems = usageInfo.findAll('li')
        expect(listItems.length).toBe(3)
    })

    it('props ile özel baslik ve açiklama gecilebilmeli', async () => {
        const customWrapper = mount(HomePage, {
            props: {
                title: 'Özel Başlık',
                description: 'Özel Açıklama'
            },
            global: {
                plugins: [router, pinia]
            }
        })

        expect(customWrapper.find('h1').text()).toBe('Özel Başlık')
        expect(customWrapper.find('.page-description').text()).toBe('Özel Açıklama')
    })

    it('dataSourceChange eventi dogru sekilde emit edilmeli', async () => {
        const restApiButton = wrapper.findAll('.data-source-btn')[1]
        await restApiButton.trigger('click')

        expect(wrapper.emitted('dataSourceChange')).toBeTruthy()
        expect(wrapper.emitted('dataSourceChange')?.[0]).toEqual(['REST_API'])
    })

    it('connectionStatusChange eventi dogru sekilde emit edilmeli', async () => {
        const restApiButton = wrapper.findAll('.data-source-btn')[1]
        await restApiButton.trigger('click')

        expect(wrapper.emitted('connectionStatusChange')).toBeTruthy()
        expect(wrapper.emitted('connectionStatusChange')?.[0]).toEqual([true])
    })
})