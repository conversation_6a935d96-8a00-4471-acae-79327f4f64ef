import type { ICriticalError } from '../types/IConnectionError.interface';
import type { IConnectionResult } from '../types/IConnectionResult.interface';
import { CriticalErrorType } from '../types/IConnectionError.interface';
import { useRouter } from 'vue-router';

/**
 * Kritik hata yönetici servisi
 * Uygulama başarısız olduğunda hata yönetimi ve yönlendirme yapar
 */
export class CriticalErrorHandler {
    private static maxRetries = 3;
    private static retryCount = 0;
    private static currentError: ICriticalError | null = null;

    /**
     * Kritik hatayı işler
     * @param error Hata nesnesi
     * @param connectionResult Bağlantı testi sonucu (varsa)
     */
    public static handleCriticalError(error: Error | ICriticalError, connectionResult?: IConnectionResult): void {
        console.error('[CriticalErrorHandler] Kritik hata tespit edildi:', error);

        // Hata nesnesini formatla
        const criticalError = this.formatCriticalError(error, connectionResult);
        this.currentError = criticalError;

        // Hata bilgisini localStorage'a kaydet
        this.saveErrorToStorage(criticalError);

        // Uygulamayı durdur ve hata ekranına yönlendir
        this.redirectToErrorScreen(criticalError);
    }

    /**
     * Hata nesnesini kritik hata formatına çevirir
     * @param error Orijinal hata
     * @param connectionResult Bağlantı testi sonucu
     * @returns ICriticalError
     */
    private static formatCriticalError(error: Error | ICriticalError, connectionResult?: IConnectionResult): ICriticalError {
        let type: CriticalErrorType = CriticalErrorType.INITIALIZATION_FAILED;
        let message = 'Bilinmeyen kritik hata';
        let details: Record<string, unknown> = {};
        let retryable = true;
        let suggestions: string[] = [];

        if (error instanceof ICriticalError) {
            return error;
        }

        if (error instanceof Error) {
            message = error.message;
            details = {
                stack: error.stack,
                name: error.name,
                originalError: error
            };

            // Hata tipini belirle
            if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
                type = CriticalErrorType.NETWORK_UNAVAILABLE;
                suggestions = [
                    'İnternet bağlantınızı kontrol edin',
                    'VPN veya proxy ayarlarınızı kontrol edin',
                    'Firewall ayarlarınızı kontrol edin'
                ];
            } else if (error.message.includes('Configuration') || error.message.includes('config')) {
                type = CriticalErrorType.CONFIGURATION_ERROR;
                retryable = false;
                suggestions = [
                    '.env dosyasını kontrol edin',
                    'Gerekli ortam değişkenlerinin ayarlandığından emin olun',
                    'Yapılandırma dosyalarını kontrol edin'
                ];
            } else if (error.message.includes('Firebase') || error.message.includes('API_KEY')) {
                type = CriticalErrorType.CONFIGURATION_ERROR;
                retryable = false;
                suggestions = [
                    'Firebase API anahtarını kontrol edin',
                    'Firebase projenizin ayarlarını kontrol edin',
                    'Ortam değişkenlerini kontrol edin'
                ];
            } else if (error.message.includes('timeout') || error.message.includes('Timeout')) {
                type = CriticalErrorType.NETWORK_UNAVAILABLE;
                suggestions = [
                    'Ağ bağlantınızı kontrol edin',
                    'Sayfayı yenileyin',
                    'Daha sonra tekrar deneyin'
                ];
            }

            // Bağlantı testi sonucu varsa bilgileri ekle
            if (connectionResult) {
                details.connectionResult = connectionResult;
            }
        }

        return {
            type,
            message,
            details,
            timestamp: new Date(),
            retryable,
            suggestions
        };
    }

    /**
     * Hata ekranına yönlendirir
     * @param error Hata bilgisi
     */
    private static redirectToErrorScreen(error: ICriticalError): void {
        // Vue router kullanarak yönlendirme
        const router = useRouter();

        // Router mevcut değilse (test ortamı için) manuel yönlendirme
        if (!router) {
            this.manualRedirect(error);
            return;
        }

        router.replace({
            name: 'critical-error',
            params: {
                errorType: error.type,
                errorMessage: btoa(JSON.stringify(error)) // Base64 encode ile parametre olarak gönder
            }
        }).catch(() => {
            // Router başarısız olursa manuel yönlendirme
            this.manualRedirect(error);
        });
    }

    /**
     * Manuel sayfa yönlendirmesi yapar
     * @param error Hata bilgisi
     */
    private static manualRedirect(error: ICriticalError): void {
        // Sayfayı manuel olarak güncelle
        window.location.hash = '#/critical-error';

        // Global hata durumunu ayarla
        (window as any).criticalError = error;

        // Sayfa yenileme (gerekirse)
        if (!document.querySelector('#critical-error-screen')) {
            this.injectErrorScreen(error);
        }
    }

    /**
     * Hata ekranını sayfaya enjekte eder
     * @param error Hata bilgisi
     */
    private static injectErrorScreen(error: ICriticalError): void {
        const errorScreen = document.createElement('div');
        errorScreen.id = 'critical-error-screen';
        errorScreen.innerHTML = `
            <div style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: #fff;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                font-family: Arial, sans-serif;
                z-index: 9999;
            ">
                <h1 style="color: #d32f2f; margin-bottom: 20px;">Uygulama Başlatılamadı</h1>
                <p style="margin-bottom: 20px; text-align: center; max-width: 600px;">
                    ${error.message}
                </p>
                <div style="margin-bottom: 30px;">
                    <h3>Çözüm Önerileri:</h3>
                    <ul style="text-align: left;">
                        ${error.suggestions?.map(s => `<li style="margin: 5px 0;">${s}</li>`).join('') || ''}
                    </ul>
                </div>
                <button onclick="window.location.reload()" style="
                    background: #1976d2;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 16px;
                ">
                    Yeniden Dene
                </button>
            </div>
        `;

        document.body.appendChild(errorScreen);
    }

    /**
     * Hata bilgisini localStorage'a kaydeder
     * @param error Hata bilgisi
     */
    private static saveErrorToStorage(error: ICriticalError): void {
        try {
            localStorage.setItem('critical_error', JSON.stringify({
                error,
                timestamp: new Date().toISOString(),
                retryCount: this.retryCount
            }));
        } catch (e) {
            console.warn('Hata bilgisi localStorage\'a kaydedilemedi:', e);
        }
    }

    /**
     * Kaydedilmiş hata bilgisini okur
     * @returns ICriticalError | null
     */
    public static getStoredError(): ICriticalError | null {
        try {
            const stored = localStorage.getItem('critical_error');
            if (stored) {
                const parsed = JSON.parse(stored);
                return parsed.error;
            }
        } catch (e) {
            console.warn('Hata bilgisi okunamadı:', e);
        }
        return null;
    }

    /**
     * Yeniden deneme sayacını artırır
     */
    public static incrementRetryCount(): void {
        this.retryCount++;
    }

    /**
     * Yeniden denenebilir olup olmadığını kontrol eder
     * @returns boolean
     */
    public static canRetry(): boolean {
        return this.retryCount < this.maxRetries;
    }

    /**
     * Yeniden deneme sayacını sıfırlar
     */
    public static resetRetryCount(): void {
        this.retryCount = 0;
    }

    /**
     * Mevcut hatayı temizler
     */
    public static clearError(): void {
        this.currentError = null;
        localStorage.removeItem('critical_error');
        this.resetRetryCount();
    }

    /**
     * Mevcut hatayı döndürür
     * @returns ICriticalError | null
     */
    public static getCurrentError(): ICriticalError | null {
        return this.currentError;
    }

    /**
     * Uygulamayı yeniden başlatır
     */
    public static async restartApplication(): Promise<void> {
        this.clearError();
        window.location.reload();
    }
}