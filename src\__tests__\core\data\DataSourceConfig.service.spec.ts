import { DataSourceConfig } from '../../../core/data/config/DataSourceConfig.service';
import { DataSourceType } from '../../../core/data/types/DataSourceType.enum';

/**
 * DataSourceConfig servisinin unit testleri
 */
describe('DataSourceConfig', () => {
    describe('getInstance', () => {
        it('singleton instance dönmeli', () => {
            const instance1 = DataSourceConfig.getInstance();
            const instance2 = DataSourceConfig.getInstance();

            expect(instance1).toBe(instance2);
        });
    });

    describe('getConfig', () => {
        it('varsayılan olarak Firebase tipinde yapılandırma dönmeli', () => {
            const config = DataSourceConfig.getInstance().getConfig();

            expect(config.type).toBe(DataSourceType.FIREBASE);
            expect(config.enabled).toBe(true);
        });
    });

    describe('getDataSourceType', () => {
        it('aktif veri kaynağı tipini dönmeli', () => {
            const type = DataSourceConfig.getInstance().getDataSourceType();

            expect(type).toBe(DataSourceType.FIREBASE);
        });
    });

    describe('isEnabled', () => {
        it('veri kaynağının aktif olduğunu dönmeli', () => {
            const enabled = DataSourceConfig.getInstance().isEnabled();

            expect(enabled).toBe(true);
        });
    });
});